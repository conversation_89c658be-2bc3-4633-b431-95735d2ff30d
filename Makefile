.PHONY: build build-auto version set-version image docker-build docker-push docker-run docker-stop docker-clean compose-up compose-down compose-logs

all: image

gobuild:
	@sh scripts/gobuild.sh

web:
	@echo "web build success"

image: gobuild web
	@echo "image build success"

v ?= $(shell sh scripts/version.sh get)

# 获取当前版本号
version:
	@echo "当前版本号: $(shell sh scripts/version.sh get)"

# 自动递增版本号构建
build:
	$(eval NEW_VERSION := $(shell sh scripts/version.sh next))
	@echo "自动递增版本号到: $(NEW_VERSION)"
	docker build -t zjl-server:$(NEW_VERSION) . && \
	docker tag zjl-server:$(NEW_VERSION) registry.rcztcs.com/zjl/zjl-server:$(NEW_VERSION) && \
	echo Docker image build success: zjl-server:$(NEW_VERSION) && \
	docker push registry.rcztcs.com/zjl/zjl-server:$(NEW_VERSION) && \
	echo Docker image pushed: registry.rcztcs.com/zjl/zjl-server:$(NEW_VERSION)


run:
	docker-compose -f docker-compose.yaml up -d
