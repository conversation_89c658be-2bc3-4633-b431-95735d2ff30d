.PHONY: build image docker-build docker-push docker-run docker-stop docker-clean compose-up compose-down compose-logs

all: image

gobuild:
	@sh scripts/gobuild.sh

web:
	@echo "web build success"

image: gobuild web
	@echo "image build success"

build:
	docker build -t zjl-server:1 . && \
	docker tag zjl-server:1 registry.rcztcs.com/zjl/zjl-server:1 && \
	echo Docker image build success: zjl-server:1 && \
	docker push registry.rcztcs.com/zjl/zjl-server:1 && \
	echo Docker image pushed: registry.rcztcs.com/zjl/zjl-server:1


run:
	docker-compose -f docker-compose.yaml up -d
