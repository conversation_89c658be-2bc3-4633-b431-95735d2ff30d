.PHONY: build image docker-build docker-push docker-run docker-stop docker-clean compose-up compose-down compose-logs

all: image

gobuild:
	@sh scripts/gobuild.sh

web:
	@echo "web build success"

image: gobuild web
	@echo "image build success"

# 默认版本号为1，可以通过 v 参数覆盖，例如: make build v=2
v ?= 1

build:
	docker build -t zjl-server:$(v) . && \
	docker tag zjl-server:$(v) registry.rcztcs.com/zjl/zjl-server:$(v) && \
	echo Docker image build success: zjl-server:$(v) && \
	docker push registry.rcztcs.com/zjl/zjl-server:$(v) && \
	echo Docker image pushed: registry.rcztcs.com/zjl/zjl-server:$(v)


run:
	docker-compose -f docker-compose.yaml up -d
