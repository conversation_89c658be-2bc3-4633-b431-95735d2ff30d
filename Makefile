.PHONY: build build-auto version set-version image run run-version stop docker-build docker-push docker-run docker-stop docker-clean compose-up compose-down compose-logs

all: image

gobuild:
	@sh scripts/gobuild.sh

web:
	@echo "web build success"

image: gobuild web
	@echo "image build success"

v ?= $(shell scripts\version.bat get)

# 获取当前版本号
version:
	@echo version:
	@scripts\version.bat get

# 自动递增版本号构建
build:
	$(eval NEW_VERSION := $(shell scripts\version.bat next))
	@echo next version: $(NEW_VERSION)
	docker build -t zjl-server:$(NEW_VERSION) . && \
	docker tag zjl-server:$(NEW_VERSION) registry.rcztcs.com/zjl/zjl-server:$(NEW_VERSION) && \
	echo Docker image build success: zjl-server:$(NEW_VERSION) && \
	docker push registry.rcztcs.com/zjl/zjl-server:$(NEW_VERSION) && \
	echo Docker image pushed: registry.rcztcs.com/zjl/zjl-server:$(NEW_VERSION)


run:
	ZJL_VERSION=$(shell scripts\version.bat get) docker-compose -f docker-compose.yaml up -d

# 使用指定版本运行，例如: make run-version v=2
run-version:
	ZJL_VERSION=$(v) docker-compose -f docker-compose.yaml up -d

# 停止服务
stop:
	docker-compose -f docker-compose.yaml down
